<template>
	<view class="modal-overlay" v-if="visible" @click="handleOverlayClick">
		<view class="modal-container" @click.stop>
			<!-- 弹窗标题 -->
			<view class="modal-header">
				<text class="modal-title">选择物料</text>
				<button class="btn-close" @click="handleCancel">
					<uni-icons type="close" size="20" color="#666"></uni-icons>
				</button>
			</view>

			<!-- 搜索框 -->
			<view class="search-section">
				<view class="search-box">
					<uni-icons type="search" size="16" color="#c0c4cc"></uni-icons>
					<input
						class="search-input"
						v-model="searchKeyword"
						placeholder="搜索物料名称或编码"
						@input="handleSearch"
					/>
					<button class="btn-clear" v-if="searchKeyword" @click="clearSearch">
						<uni-icons type="clear" size="14" color="#c0c4cc"></uni-icons>
					</button>
				</view>
			</view>

			<!-- 物料列表 -->
			<scroll-view scroll-y="true" class="material-list" @scrolltolower="loadMore">
				<view class="list-container">
					<view 
						class="material-item" 
						v-for="(item, index) in materialList" 
						:key="item.materialId || index"
						@click="handleSelectMaterial(item)"
					>
						<view class="material-info">
							<view class="material-name">{{ item.materialName || '-' }}</view>
							<view class="material-code">编码：{{ item.materialCode || '-' }}</view>
							<view class="material-spec" v-if="item.spec">规格：{{ item.spec }}</view>
							<view class="material-unit">单位：{{ item.unitName || '-' }}</view>
						</view>
						<uni-icons type="right" size="16" color="#c0c4cc"></uni-icons>
					</view>

					<!-- 加载更多 -->
					<view class="load-more" v-if="hasMore">
						<uni-icons type="spinner-cycle" size="20" color="#c0c4cc"></uni-icons>
						<text class="load-text">加载中...</text>
					</view>

					<!-- 没有更多数据 -->
					<view class="no-more" v-else-if="materialList.length > 0">
						<text class="no-more-text">没有更多数据了</text>
					</view>
				</view>

				<!-- 空状态 -->
				<view class="empty-state" v-if="materialList.length === 0 && !loading">
					<uni-icons type="info" size="48" color="#c0c4cc"></uni-icons>
					<text class="empty-text">{{ searchKeyword ? '未找到相关物料' : '暂无物料数据' }}</text>
				</view>
			</scroll-view>

			<!-- 弹窗底部 -->
			<view class="modal-footer">
				<button class="btn-cancel" @click="handleCancel">取消</button>
			</view>
		</view>
	</view>
</template>

<script>
import { getWorkOrderDetailListByBizOrderIdApi } from '../../../../../../api/scm/mfg/workorder'
import { getUnitApi } from '../../../../../../api/scm/base/unit'

export default {
	name: 'MaterialSelector',
	props: {
		// 弹窗显示状态
		visible: {
			type: Boolean,
			default: false
		},
		// 工单ID
		workId: {
			type: [Number, String],
			default: null
		}
	},
	data() {
		return {
			// 搜索关键词
			searchKeyword: '',
			// 物料列表
			materialList: [],
			// 原始物料列表（用于搜索过滤）
			originalMaterialList: [],
			// 加载状态
			loading: false,
			// 是否有更多数据
			hasMore: false,
			// 单位映射缓存
			unitMap: {}
		}
	},
	watch: {
		// 监听弹窗显示状态
		visible: {
			handler(newVal) {
				if (newVal) {
					this.loadMaterialList()
				} else {
					this.resetData()
				}
			},
			immediate: true
		}
	},
	methods: {
		// 加载物料列表
		async loadMaterialList() {
			if (!this.workId) {
				console.warn('工单ID为空，无法加载物料列表')
				return
			}

			try {
				this.loading = true
				
				// 从投料单获取物料数据
				const response = await getWorkOrderDetailListByBizOrderIdApi(this.workId)
				if (response && response.data && Array.isArray(response.data)) {
					// 获取单位信息
					await this.loadUnitInfo(response.data)
					
					// 转换数据格式
					this.originalMaterialList = response.data.map(item => ({
						materialId: item.materialId,
						materialName: item.materialName,
						materialCode: item.materialCode,
						spec: item.spec,
						unit: item.unit,
						unitName: this.unitMap[item.unit] || '',
						plannedQuantity: item.plannedQuantity || item.quantity || 0
					}))
					
					this.materialList = [...this.originalMaterialList]
				} else {
					this.originalMaterialList = []
					this.materialList = []
				}
			} catch (error) {
				console.error('加载物料列表失败:', error)
				uni.showToast({
					title: '加载物料列表失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},

		// 加载单位信息
		async loadUnitInfo(materialList) {
			const unitIds = [...new Set(materialList.map(item => item.unit).filter(Boolean))]
			
			for (const unitId of unitIds) {
				if (!this.unitMap[unitId]) {
					try {
						const unitInfo = await getUnitApi(unitId)
						if (unitInfo && unitInfo.data) {
							this.unitMap[unitId] = unitInfo.data.name
						}
					} catch (error) {
						console.error(`获取单位信息失败 (ID: ${unitId}):`, error)
						this.unitMap[unitId] = ''
					}
				}
			}
		},

		// 处理搜索
		handleSearch() {
			if (!this.searchKeyword.trim()) {
				this.materialList = [...this.originalMaterialList]
				return
			}

			const keyword = this.searchKeyword.toLowerCase()
			this.materialList = this.originalMaterialList.filter(item => {
				return (item.materialName && item.materialName.toLowerCase().includes(keyword)) ||
					   (item.materialCode && item.materialCode.toLowerCase().includes(keyword))
			})
		},

		// 清除搜索
		clearSearch() {
			this.searchKeyword = ''
			this.materialList = [...this.originalMaterialList]
		},

		// 加载更多（暂时不需要分页）
		loadMore() {
			// 当前从投料单获取数据，不需要分页
		},

		// 选择物料
		handleSelectMaterial(material) {
			this.$emit('confirm', material)
		},

		// 处理遮罩层点击
		handleOverlayClick() {
			this.handleCancel()
		},

		// 取消
		handleCancel() {
			this.$emit('cancel')
		},

		// 重置数据
		resetData() {
			this.searchKeyword = ''
			this.materialList = []
			this.originalMaterialList = []
			this.loading = false
			this.hasMore = false
		}
	}
}
</script>

<style scoped>
/* 弹窗遮罩 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1001;
	backdrop-filter: blur(4px);
}

/* 弹窗容器 */
.modal-container {
	width: 90%;
	max-width: 500px;
	height: 70vh;
	background-color: #fff;
	border-radius: 16px;
	display: flex;
	flex-direction: column;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* 弹窗标题 */
.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px 20px 16px;
	border-bottom: 1px solid #f0f0f0;
}

.modal-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

.btn-close {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 32px;
	height: 32px;
	border: none;
	border-radius: 6px;
	background-color: #f8f9fa;
	transition: all 0.2s ease;
}

.btn-close:active {
	background-color: #e9ecef;
	transform: scale(0.95);
}

/* 搜索区域 */
.search-section {
	padding: 16px 20px;
	border-bottom: 1px solid #f0f0f0;
}

.search-box {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 10px 16px;
	background-color: #f8f9fa;
	border-radius: 8px;
	border: 1px solid #e9ecef;
}

.search-input {
	flex: 1;
	font-size: 15px;
	color: #333;
	background: transparent;
	border: none;
	outline: none;
}

.search-input::placeholder {
	color: #c0c4cc;
}

.btn-clear {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 20px;
	height: 20px;
	border: none;
	border-radius: 50%;
	background-color: transparent;
}

/* 物料列表 */
.material-list {
	flex: 1;
	overflow-y: auto;
}

.list-container {
	padding: 0 20px;
}

.material-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16px 0;
	border-bottom: 1px solid #f0f0f0;
	transition: background-color 0.2s ease;
}

.material-item:active {
	background-color: #f8f9fa;
}

.material-item:last-child {
	border-bottom: none;
}

.material-info {
	flex: 1;
}

.material-name {
	font-size: 16px;
	font-weight: 500;
	color: #333;
	margin-bottom: 6px;
}

.material-code,
.material-spec,
.material-unit {
	font-size: 14px;
	color: #666;
	margin-bottom: 4px;
}

.material-code:last-child,
.material-spec:last-child,
.material-unit:last-child {
	margin-bottom: 0;
}

/* 加载更多 */
.load-more {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8px;
	padding: 20px;
}

.load-text {
	font-size: 14px;
	color: #c0c4cc;
}

.no-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20px;
}

.no-more-text {
	font-size: 14px;
	color: #c0c4cc;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 20px;
	text-align: center;
}

.empty-text {
	font-size: 16px;
	color: #666;
	margin-top: 12px;
	font-weight: 500;
}

/* 弹窗底部 */
.modal-footer {
	padding: 16px 20px 20px;
	border-top: 1px solid #f0f0f0;
}

.btn-cancel {
	width: 100%;
	height: 44px;
	border: 1px solid #e1e5e9;
	border-radius: 8px;
	background-color: #fff;
	color: #6c757d;
	font-size: 16px;
	font-weight: 500;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}

.btn-cancel:active {
	background-color: #f8f9fa;
	transform: scale(0.98);
}
</style>
