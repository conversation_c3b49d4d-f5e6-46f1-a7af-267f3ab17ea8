import request from "../../../../utils/request";

// 查询报工损耗分页
export function getReportLossPageApi(params) {
    return request({
        url: '/scm/mfg/report-loss/page',
        method: 'GET',
        params
    })
}

// 查询报工损耗详情
export function getReportLossApi(id) {
    return request({
        url: '/scm/mfg/report-loss/get?id=' + id,
        method: 'GET'
    })
}

// 新增报工损耗
export function createReportLossApi(data) {
    return request({
        url: '/scm/mfg/report-loss/create',
        method: 'POST',
        data
    })
}

// 修改报工损耗
export function updateReportLossApi(data) {
    return request({
        url: '/scm/mfg/report-loss/update',
        method: 'PUT',
        data
    })
}

// 删除报工损耗
export function deleteReportLossApi(id) {
    return request({
        url: '/scm/mfg/report-loss/delete?id=' + id,
        method: 'DELETE'
    })
}

// 根据报工单ID查询报工损耗列表
export function getReportLossByReportIdApi(reportId) {
    return request({
        url: '/scm/mfg/report-loss/list-by-report-id?reportId=' + reportId,
        method: 'GET'
    })
}