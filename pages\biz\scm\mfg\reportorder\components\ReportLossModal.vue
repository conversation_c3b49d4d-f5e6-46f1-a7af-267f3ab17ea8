<template>
	<view class="modal-overlay" v-if="visible" @click="handleOverlayClick">
		<view class="modal-container" @click.stop>
			<!-- 弹窗标题 -->
			<view class="modal-header">
				<text class="modal-title">{{ isEdit ? '编辑损耗' : '新增损耗' }}</text>
				<button class="btn-close" @click="handleCancel">
					<uni-icons type="close" size="20" color="#666"></uni-icons>
				</button>
			</view>

			<!-- 弹窗内容 -->
			<scroll-view scroll-y="true" class="modal-content">
				<!-- 物料选择 -->
				<view class="form-item">
					<text class="form-label required">物料</text>
					<view class="material-selector" @click="handleSelectMaterial">
						<view class="material-display" v-if="localFormData.materialId">
							<view class="material-name">{{ localFormData.materialName }}</view>
							<view class="material-code">{{ localFormData.materialCode }}</view>
							<view class="material-spec" v-if="localFormData.spec">{{ localFormData.spec }}</view>
						</view>
						<view class="material-placeholder" v-else>
							<text class="placeholder-text">请选择物料</text>
						</view>
						<uni-icons type="right" size="16" color="#c0c4cc"></uni-icons>
					</view>
				</view>

				<!-- 损耗数量 -->
				<view class="form-item">
					<text class="form-label required">损耗数量</text>
					<view class="quantity-input-group">
						<input
							class="form-input"
							type="digit"
							:value="localFormData.lossQuantity || ''"
							@input="handleQuantityInput"
							placeholder="请输入损耗数量"
						/>
						<text class="unit-text">{{ localFormData.lossUnitName || '单位' }}</text>
					</view>
				</view>

				<!-- 备注 -->
				<view class="form-item">
					<text class="form-label">备注</text>
					<textarea
						class="form-textarea"
						v-model="localFormData.remark"
						placeholder="请输入备注信息（可选）"
						:maxlength="200"
						show-count
					></textarea>
				</view>
			</scroll-view>

			<!-- 弹窗底部按钮 -->
			<view class="modal-footer">
				<button class="btn-cancel" @click="handleCancel">取消</button>
				<button class="btn-confirm" @click="handleConfirm" :disabled="!canSubmit">
					{{ isEdit ? '保存' : '新增' }}
				</button>
			</view>
		</view>

		<!-- 物料选择弹窗 -->
		<MaterialSelector
			ref="materialSelectorRef"
			:visible="materialSelectorVisible"
			:work-id="workId"
			@confirm="handleMaterialConfirm"
			@cancel="handleMaterialCancel"
		/>
	</view>
</template>

<script>
import MaterialSelector from './MaterialSelector.vue'

export default {
	name: 'ReportLossModal',
	components: {
		MaterialSelector
	},
	props: {
		// 弹窗显示状态
		visible: {
			type: Boolean,
			default: false
		},
		// 表单数据
		formData: {
			type: Object,
			default: () => ({})
		},
		// 工单ID
		workId: {
			type: [Number, String],
			default: null
		}
	},
	data() {
		return {
			// 本地表单数据
			localFormData: {
				id: null,
				reportId: null,
				workId: null,
				materialId: null,
				materialName: '',
				materialCode: '',
				spec: '',
				lossQuantity: 0,
				lossUnit: null,
				lossUnitName: '',
				remark: ''
			},
			// 物料选择弹窗显示状态
			materialSelectorVisible: false
		}
	},
	computed: {
		// 是否为编辑模式
		isEdit() {
			return !!(this.localFormData.id)
		},
		// 是否可以提交
		canSubmit() {
			return this.localFormData.materialId && 
				   this.localFormData.lossQuantity > 0
		}
	},
	watch: {
		// 监听弹窗显示状态
		visible: {
			handler(newVal) {
				if (newVal) {
					this.initFormData()
				}
			},
			immediate: true
		},
		// 监听表单数据变化
		formData: {
			handler() {
				if (this.visible) {
					this.initFormData()
				}
			},
			deep: true
		}
	},
	methods: {
		// 初始化表单数据
		initFormData() {
			this.localFormData = {
				id: this.formData?.id || null,
				reportId: this.formData?.reportId || null,
				workId: this.formData?.workId || this.workId,
				materialId: this.formData?.materialId || null,
				materialName: this.formData?.materialName || '',
				materialCode: this.formData?.materialCode || '',
				spec: this.formData?.spec || '',
				lossQuantity: this.formData?.lossQuantity || 0,
				lossUnit: this.formData?.lossUnit || null,
				lossUnitName: this.formData?.lossUnitName || '',
				remark: this.formData?.remark || ''
			}
		},

		// 处理遮罩层点击
		handleOverlayClick() {
			this.handleCancel()
		},

		// 选择物料
		handleSelectMaterial() {
			this.materialSelectorVisible = true
		},

		// 物料选择确认
		handleMaterialConfirm(material) {
			this.localFormData.materialId = material.materialId
			this.localFormData.materialName = material.materialName
			this.localFormData.materialCode = material.materialCode
			this.localFormData.spec = material.spec
			this.localFormData.lossUnit = material.unit
			this.localFormData.lossUnitName = material.unitName
			this.materialSelectorVisible = false
		},

		// 物料选择取消
		handleMaterialCancel() {
			this.materialSelectorVisible = false
		},

		// 处理数量输入
		handleQuantityInput(e) {
			const value = e.detail.value.replace(/[^\d.]/g, '')
			this.localFormData.lossQuantity = value ? parseFloat(value) : 0
		},

		// 确认提交
		handleConfirm() {
			if (!this.canSubmit) {
				uni.showToast({
					title: '请完善必填信息',
					icon: 'none'
				})
				return
			}

			// 验证数量
			if (this.localFormData.lossQuantity <= 0) {
				uni.showToast({
					title: '损耗数量必须大于0',
					icon: 'none'
				})
				return
			}

			// 触发确认事件
			this.$emit('confirm', { ...this.localFormData })
		},

		// 取消
		handleCancel() {
			this.$emit('cancel')
		}
	}
}
</script>

<style scoped>
/* 弹窗遮罩 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	backdrop-filter: blur(4px);
}

/* 弹窗容器 */
.modal-container {
	width: 90%;
	max-width: 500px;
	max-height: 80vh;
	background-color: #fff;
	border-radius: 16px;
	display: flex;
	flex-direction: column;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* 弹窗标题 */
.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px 20px 16px;
	border-bottom: 1px solid #f0f0f0;
}

.modal-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

.btn-close {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 32px;
	height: 32px;
	border: none;
	border-radius: 6px;
	background-color: #f8f9fa;
	transition: all 0.2s ease;
}

.btn-close:active {
	background-color: #e9ecef;
	transform: scale(0.95);
}

/* 弹窗内容 */
.modal-content {
	flex: 1;
	padding: 20px;
	overflow-y: auto;
}

/* 表单项 */
.form-item {
	margin-bottom: 20px;
}

.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	display: block;
	font-size: 15px;
	font-weight: 500;
	color: #333;
	margin-bottom: 10px;
	position: relative;
}

.form-label.required::after {
	content: '*';
	color: #ff4757;
	margin-left: 4px;
	font-size: 16px;
}

/* 物料选择器 */
.material-selector {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px 16px;
	border: 1px solid #e1e5e9;
	border-radius: 8px;
	background-color: #fff;
	transition: border-color 0.2s ease;
}

.material-selector:active {
	border-color: #007AFF;
}

.material-display {
	flex: 1;
}

.material-name {
	font-size: 15px;
	font-weight: 500;
	color: #333;
	margin-bottom: 4px;
}

.material-code {
	font-size: 13px;
	color: #666;
	margin-bottom: 2px;
}

.material-spec {
	font-size: 12px;
	color: #999;
}

.material-placeholder {
	flex: 1;
}

.placeholder-text {
	font-size: 15px;
	color: #c0c4cc;
}

/* 数量输入组 */
.quantity-input-group {
	display: flex;
	align-items: center;
	gap: 12px;
}

.form-input {
	flex: 1;
	height: 44px;
	padding: 0 14px;
	border: 1px solid #e1e5e9;
	border-radius: 8px;
	font-size: 15px;
	background-color: #fff;
	box-sizing: border-box;
	transition: border-color 0.2s ease;
}

.form-input:focus {
	border-color: #007AFF;
	outline: none;
}

.unit-text {
	font-size: 15px;
	color: #6c757d;
	font-weight: 500;
	min-width: 40px;
}

/* 文本域 */
.form-textarea {
	width: 100%;
	min-height: 88px;
	padding: 12px 14px;
	border: 1px solid #e1e5e9;
	border-radius: 8px;
	font-size: 15px;
	background-color: #fff;
	box-sizing: border-box;
	resize: none;
	line-height: 1.5;
	transition: border-color 0.2s ease;
}

.form-textarea:focus {
	border-color: #007AFF;
	outline: none;
}

/* 弹窗底部 */
.modal-footer {
	display: flex;
	gap: 12px;
	padding: 16px 20px 20px;
	border-top: 1px solid #f0f0f0;
}

.btn-cancel,
.btn-confirm {
	flex: 1;
	height: 44px;
	border-radius: 8px;
	font-size: 16px;
	font-weight: 500;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}

.btn-cancel {
	border: 1px solid #e1e5e9;
	background-color: #fff;
	color: #6c757d;
}

.btn-cancel:active {
	background-color: #f8f9fa;
	transform: scale(0.98);
}

.btn-confirm {
	border: none;
	background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
	color: #fff;
	box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.btn-confirm:active {
	transform: scale(0.98);
	box-shadow: 0 2px 8px rgba(0, 122, 255, 0.4);
}

.btn-confirm:disabled {
	background: #e9ecef;
	color: #6c757d;
	box-shadow: none;
	transform: none;
}
</style>
