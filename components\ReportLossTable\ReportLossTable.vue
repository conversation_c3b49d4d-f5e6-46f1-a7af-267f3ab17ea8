<template>
	<view class="report-loss-table">
		<!-- 表格头部 -->
		<view class="table-header">
			<text class="header-title">报工损耗信息</text>
			<button 
				class="add-btn" 
				@click="handleAdd"
				:disabled="disabled"
			>
				<uni-icons type="plus" size="16" color="#fff"></uni-icons>
				<text class="add-text">新增</text>
			</button>
		</view>

		<!-- 表格内容 -->
		<view class="table-container">
			<view v-if="tableData.length === 0" class="empty-state">
				<uni-icons type="info" size="48" color="#c0c4cc"></uni-icons>
				<text class="empty-text">暂无损耗数据</text>
			</view>
			
			<view v-else class="table-content">
				<view 
					v-for="(item, index) in tableData" 
					:key="index" 
					class="table-row"
				>
					<!-- 物料信息 -->
					<view class="material-info">
						<view class="material-header">
							<text class="material-name">{{ item.materialName || '-' }}</text>
							<view class="row-actions" v-if="!disabled">
								<button 
									class="edit-btn" 
									@click="handleEdit(index)"
								>
									<uni-icons type="compose" size="14" color="#007AFF"></uni-icons>
								</button>
								<button 
									class="delete-btn" 
									@click="handleDelete(index)"
								>
									<uni-icons type="trash" size="14" color="#ff4757"></uni-icons>
								</button>
							</view>
						</view>
						<view class="material-details">
							<view class="detail-item">
								<text class="label">编码：</text>
								<text class="value">{{ item.materialCode || '-' }}</text>
							</view>
							<view class="detail-item">
								<text class="label">规格：</text>
								<text class="value">{{ item.spec || '-' }}</text>
							</view>
							<view class="detail-item">
								<text class="label">计划：</text>
								<text class="value">{{ item.plannedQuantity || '-' }}</text>
							</view>
						</view>
					</view>

					<!-- 损耗信息 -->
					<view class="loss-info">
						<view class="loss-item">
							<text class="loss-label">损耗数量</text>
							<text class="loss-value">{{ item.lossQuantity || 0 }}</text>
						</view>
						<view class="loss-item">
							<text class="loss-label">损耗单位</text>
							<text class="loss-value">{{ item.lossUnitName || '-' }}</text>
						</view>
						<view class="loss-item" v-if="item.remark">
							<text class="loss-label">备注</text>
							<text class="loss-value">{{ item.remark }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 报工损耗编辑弹窗 -->
		<ReportLossModal
			ref="reportLossModalRef"
			:visible="modalVisible"
			:formData="currentFormData"
			:materialOptions="materialOptions"
			:unitOptions="unitOptions"
			@confirm="handleModalConfirm"
			@cancel="handleModalCancel"
		/>
	</view>
</template>

<script>
import ReportLossModal from './ReportLossModal.vue'

export default {
	name: 'ReportLossTable',
	components: {
		ReportLossModal
	},
	props: {
		// 表格数据
		value: {
			type: Array,
			default: () => []
		},
		// 是否禁用
		disabled: {
			type: Boolean,
			default: false
		},
		// 物料选项
		materialOptions: {
			type: Array,
			default: () => []
		},
		// 单位选项
		unitOptions: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			tableData: [],
			modalVisible: false,
			currentFormData: {},
			editIndex: -1
		}
	},
	watch: {
		value: {
			handler(newVal) {
				this.tableData = [...(newVal || [])]
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		// 新增损耗
		handleAdd() {
			this.editIndex = -1
			this.currentFormData = {
				materialId: '',
				materialName: '',
				materialCode: '',
				spec: '',
				plannedQuantity: 0,
				lossQuantity: 0,
				lossUnit: '',
				lossUnitName: '',
				remark: ''
			}
			this.modalVisible = true
		},

		// 编辑损耗
		handleEdit(index) {
			this.editIndex = index
			this.currentFormData = { ...this.tableData[index] }
			this.modalVisible = true
		},

		// 删除损耗
		handleDelete(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这条损耗记录吗？',
				success: (res) => {
					if (res.confirm) {
						this.tableData.splice(index, 1)
						this.emitChange()
					}
				}
			})
		},

		// 弹窗确认
		handleModalConfirm(formData) {
			if (this.editIndex >= 0) {
				// 编辑模式
				this.$set(this.tableData, this.editIndex, { ...formData })
			} else {
				// 新增模式
				this.tableData.push({ ...formData })
			}
			this.modalVisible = false
			this.emitChange()
		},

		// 弹窗取消
		handleModalCancel() {
			this.modalVisible = false
		},

		// 触发数据变化事件
		emitChange() {
			this.$emit('input', [...this.tableData])
			this.$emit('change', [...this.tableData])
		},

		// 获取表格数据
		getData() {
			return [...this.tableData]
		},

		// 设置表格数据
		setData(data) {
			this.tableData = [...(data || [])]
		},

		// 验证数据
		validate() {
			for (let i = 0; i < this.tableData.length; i++) {
				const item = this.tableData[i]
				if (!item.materialId) {
					uni.showToast({
						title: `第${i + 1}行：请选择物料`,
						icon: 'none'
					})
					return false
				}
				if (!item.lossQuantity || item.lossQuantity <= 0) {
					uni.showToast({
						title: `第${i + 1}行：请输入有效的损耗数量`,
						icon: 'none'
					})
					return false
				}
			}
			return true
		}
	}
}
</script>

<style scoped>
.report-loss-table {
	background-color: #fff;
	border-radius: 12px;
	overflow: hidden;
}

/* 表格头部 */
.table-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px 20px;
	background-color: #f8f9fa;
	border-bottom: 1px solid #e9ecef;
}

.header-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

.add-btn {
	display: flex;
	align-items: center;
	gap: 6px;
	padding: 8px 16px;
	background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
	color: #fff;
	border: none;
	border-radius: 8px;
	font-size: 14px;
	font-weight: 500;
}

.add-btn:disabled {
	background: #e9ecef;
	color: #6c757d;
}

.add-text {
	color: inherit;
}

/* 表格容器 */
.table-container {
	max-height: 400px;
	overflow-y: auto;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 20px;
	gap: 12px;
}

.empty-text {
	font-size: 14px;
	color: #909399;
}

/* 表格行 */
.table-row {
	padding: 16px 20px;
	border-bottom: 1px solid #f0f0f0;
}

.table-row:last-child {
	border-bottom: none;
}

/* 物料信息 */
.material-info {
	margin-bottom: 12px;
}

.material-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
}

.material-name {
	font-size: 15px;
	font-weight: 600;
	color: #333;
	flex: 1;
}

.row-actions {
	display: flex;
	gap: 8px;
}

.edit-btn,
.delete-btn {
	width: 32px;
	height: 32px;
	border: none;
	border-radius: 6px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f8f9fa;
}

.edit-btn:active {
	background-color: #e9ecef;
}

.delete-btn:active {
	background-color: #f8d7da;
}

.material-details {
	display: flex;
	flex-wrap: wrap;
	gap: 12px;
}

.detail-item {
	display: flex;
	align-items: center;
	font-size: 13px;
}

.label {
	color: #909399;
	margin-right: 4px;
	font-weight: 500;
}

.value {
	color: #606266;
}

/* 损耗信息 */
.loss-info {
	display: flex;
	flex-wrap: wrap;
	gap: 16px;
}

.loss-item {
	display: flex;
	flex-direction: column;
	gap: 4px;
	min-width: 80px;
}

.loss-label {
	font-size: 12px;
	color: #909399;
	font-weight: 500;
}

.loss-value {
	font-size: 14px;
	color: #333;
	font-weight: 600;
}
</style>
